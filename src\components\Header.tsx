"use client";
import Link from "next/link";
import { useState, useEffect } from "react";
import { Building2, User } from "lucide-react";
import { useRouter } from "next/navigation";
import Logo from "./Logo";
import { FaFacebookF, <PERSON>a<PERSON>wi<PERSON>, FaLinkedinIn, FaInstagram } from 'react-icons/fa';

export default function Header() {
  const router = useRouter();
  const [menuOpen, setMenuOpen] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [userType, setUserType] = useState<'individual' | 'corporate' | null>(null);

  useEffect(() => {
    // Check authentication status
    const user = localStorage.getItem('user');
    const type = localStorage.getItem('userType') as 'individual' | 'corporate' | null;
    
    setIsLoggedIn(!!user);
    setUserType(type);
  }, []);

  const handleGetStarted = (e: React.MouseEvent) => {
    e.preventDefault();
    if (isLoggedIn) {
      // Redirect to appropriate dashboard
      router.push(userType === 'corporate' ? '/dashboard/lender' : '/dashboard/borrower');
    } else {
      setShowModal(true);
    }
  };

  const handleLogout = () => {
    localStorage.removeItem('user');
    localStorage.removeItem('userType');
    setIsLoggedIn(false);
    setUserType(null);
    router.push('/');
  };

  return (
    <>
      <header className="relative mb-3">
        <div className="flex flex-col py-4 px-4 md:px-16 border-t border-gray-100 mt-0">
          {/* Social Media Icons */}
          <div className="flex justify-end gap-4 mb-2">
            <Link href="#" aria-label="Facebook" className="text-gray-400 hover:text-gray-600">
              <FaFacebookF size={20} />
            </Link>
            <Link href="#" aria-label="Twitter" className="text-gray-400 hover:text-gray-600">
              <FaTwitter size={20} />
            </Link>
            <Link href="#" aria-label="LinkedIn" className="text-gray-400 hover:text-gray-600">
              <FaLinkedinIn size={20} />
            </Link>
            <Link href="#" aria-label="Instagram" className="text-gray-400 hover:text-gray-600">
              <FaInstagram size={20} />
            </Link>
          </div>
        </div>
        <div className="w-full border-b border-gray-200 mb-4"></div> 
        <div className="flex justify-between items-center px-4 md:px-16 relative">
          <Logo  width={100} height={60}/>
          {/* Hamburger for mobile - always top right */}
          <button
            className="md:hidden flex flex-col justify-center items-center w-10 h-10 absolute right-4 top-1/2 -translate-y-1/2"
            onClick={() => setMenuOpen(!menuOpen)}
            aria-label="Open menu"
          >
            <span className="block w-6 h-0.5 bg-black mb-1"></span>
            <span className="block w-6 h-0.5 bg-black mb-1"></span>
            <span className="block w-6 h-0.5 bg-black"></span>
          </button>
          {/* Desktop nav */}
          <nav className="hidden md:flex items-center gap-8">
            <Link href="/" className="font-medium">HOME</Link>
            <Link href="/about" className="font-medium">ABOUT</Link>
            <Link href="/lend" className="font-medium">LEND</Link>
            <Link href="/borrow" className="font-medium">BORROW</Link>
            <Link href="/marketplace" className="font-medium">MARKETPLACE</Link>
            <Link href="/pages" className="font-medium">PAGES</Link>
            {isLoggedIn ? (
              <div className="flex items-center gap-4">
                <Link
                  href={userType === 'corporate' ? '/dashboard/lender' : '/dashboard/borrower'}
                  className="bg-[#1A0505] text-white px-4 py-2 rounded-full text-center"
                >
                  Dashboard
                </Link>
                <button
                  onClick={handleLogout}
                  className="text-gray-600 hover:text-gray-800 font-medium"
                >
                  Logout
                </button>
              </div>
            ) : (
              <div className="flex items-center gap-4">
                <button
                  onClick={handleGetStarted}
                  className="bg-[#1A0505] text-white px-4 py-2 rounded-full text-center"
                >
                  GET STARTED
                </button>
              </div>
            )}
          </nav>
        </div>
        {/* Mobile menu */}
        {menuOpen && (
          <div className="md:hidden px-4 pb-4">
            <nav className="flex flex-col gap-4 mt-2">
              <Link href="/" className="font-medium" onClick={() => setMenuOpen(false)}>HOME</Link>
              <Link href="/about" className="font-medium" onClick={() => setMenuOpen(false)}>ABOUT</Link>
              <Link href="/lend" className="font-medium" onClick={() => setMenuOpen(false)}>LEND</Link>
              <Link href="/borrow" className="font-medium" onClick={() => setMenuOpen(false)}>BORROW</Link>
              <Link href="/marketplace" className="font-medium" onClick={() => setMenuOpen(false)}>MARKETPLACE</Link>
              <Link href="/pages" className="font-medium" onClick={() => setMenuOpen(false)}>PAGES</Link>
              {isLoggedIn ? (
                <div className="flex flex-col gap-2">
                  <Link
                    href={userType === 'corporate' ? '/dashboard/lender' : '/dashboard/borrower'}
                    className="bg-[#1A0505] text-white px-4 py-2 rounded-full text-center"
                    onClick={() => setMenuOpen(false)}
                  >
                    Dashboard
                  </Link>
                  <button
                    onClick={() => {
                      handleLogout();
                      setMenuOpen(false);
                    }}
                    className="text-gray-600 hover:text-gray-800 font-medium text-left"
                  >
                    Logout
                  </button>
                </div>
              ) : (
                <div className="flex flex-col gap-2">
                  <button
                    onClick={(e) => {
                      e.preventDefault();
                      setShowModal(true);
                      setMenuOpen(false);
                    }}
                    className="bg-[#1A0505] text-white px-4 py-2 rounded-full text-center"
                  >
                    GET STARTED
                  </button>
                </div>
              )}
            </nav>
          </div>
        )}
      </header>

      {/* Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-8 max-w-2xl w-full mx-4">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold">Choose Account Type</h2>
              <button
                onClick={() => setShowModal(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                ✕
              </button>
            </div>
            <div className="grid md:grid-cols-2 gap-6">
              {/* Individual Card */}
              <Link
                href="/auth/individual/signup"
                className="border rounded-lg p-6 hover:border-[#1A0505] transition-colors cursor-pointer"
                onClick={() => setShowModal(false)}
              >
                <div className="flex flex-col items-center text-center">
                  <User className="w-16 h-16 mb-4 text-[#1A0505]" />
                  <h3 className="text-xl font-semibold mb-2">For Individual</h3>
                  <p className="text-gray-600">Create an account for personal use</p>
                </div>
              </Link>

              {/* Corporate Card */}
              <Link
                href="/auth/corporate/signup"
                className="border rounded-lg p-6 hover:border-[#1A0505] transition-colors cursor-pointer"
                onClick={() => setShowModal(false)}
              >
                <div className="flex flex-col items-center text-center">
                  <Building2 className="w-16 h-16 mb-4 text-[#1A0505]" />
                  <h3 className="text-xl font-semibold mb-2">For Corporate</h3>
                  <p className="text-gray-600">Create an account for your business</p>
                </div>
              </Link>
            </div>
          </div>
        </div>
      )}
    </>
  );
}







